# 联通资费爬虫系统业务说明文档

## 📋 系统概述

### 系统目标
联通资费爬虫系统是一个自动化数据收集工具，专门用于从中国联通官方网站自动获取和整理各种资费套餐信息。就像派遣一个"数字助手"定期访问联通官网，把所有公开的资费信息收集回来，整理成标准格式保存到我们的数据库中。

### 为什么需要这个系统？
- **信息及时性**：联通的资费信息经常更新，人工收集效率低且容易遗漏
- **数据标准化**：将不同格式的资费信息统一整理成标准格式，便于分析和比较
- **全面覆盖**：自动收集全国各省份的资费信息，确保数据完整性
- **减少人工成本**：替代人工手动收集，提高工作效率

### 系统架构概览

```mermaid
graph TB
    subgraph "外部系统"
        A[联通官网]
        B[业务调用方]
    end

    subgraph "爬虫系统核心"
        C[任务调度器]
        D[数据收集器]
        E[数据解析器]
        F[数据存储器]
    end

    subgraph "数据存储"
        G[任务数据库]
        H[资费数据库]
        I[日志数据库]
    end

    subgraph "监控告警"
        J[系统监控]
        K[异常告警]
    end

    A --> D
    C --> D
    D --> E
    E --> F
    F --> H
    C --> G
    D --> I
    E --> I
    F --> I

    J --> C
    J --> D
    J --> E
    J --> F
    K --> J

    H --> B

    style A fill:#ffcdd2
    style B fill:#e1f5fe
    style C fill:#fff3e0
    style H fill:#c8e6c9
```

## 🎯 主要功能模块

### 1. 任务管理模块
**功能说明**：负责创建和管理数据收集任务
- 为每个省份创建独立的收集任务
- 记录任务执行状态（进行中、已完成、失败等）
- 跟踪每次收集的数据量和完成时间

### 2. 数据收集模块
**功能说明**：模拟真实用户访问联通官网，获取资费数据
- 自动访问联通官方资费查询页面
- 模拟真实浏览器行为，避免被网站识别为机器人
- 按照省份和资费类型分类收集数据

### 3. 数据解析模块
**功能说明**：将收集到的原始数据转换成结构化信息
- 提取资费名称、价格、套餐内容等关键信息
- 识别不同类型的资费（个人套餐、企业套餐、增值服务等）
- 处理特殊格式和异常数据

### 4. 数据存储模块
**功能说明**：将整理好的数据保存到数据库
- 去除重复数据
- 按照统一格式存储
- 建立数据版本管理

## 🔄 联通资费爬虫执行流程

### 核心执行步骤

1. **接收任务** → 系统接收到爬取指令，包含省份信息

2. **双线并行收集**：
   - **线路A**：收集全网资费（全国通用的套餐）
   - **线路B**：收集本省资费（该省份特有的套餐）

3. **具体收集过程**（每条线路都执行）：
   ```
   获取省份城市列表 → 获取资费分类菜单 → 逐个分类收集资费详情
   ```

4. **资费分类处理**：
   - 个人套餐（4G/5G）
   - 企业套餐
   - 固话宽带
   - 叠加包服务
   - 新业务
   - 营销活动

5. **数据处理**：
   - 解析收集到的JSON数据
   - 提取关键信息（资费名称、价格、套餐内容等）
   - 去除重复数据

6. **结果检查**：
   - 如果全网资费数量 = 0 或 本省资费数量 = 0 → 任务失败
   - 如果两个都 > 0 → 任务成功

7. **保存数据** → 批量插入数据库，更新任务状态为完成

### 关键执行要点
- **必须同时成功收集全网和本省两类数据**，任何一类失败都算任务失败
- **每个省份独立执行**，互不影响
- **遇到网络错误会自动重试3次**

### 执行流程图

```mermaid
graph TD
    A[接收任务指令] --> B[创建省份任务]
    B --> C{双线并行收集}

    C --> D[线路A: 收集全网资费]
    C --> E[线路B: 收集本省资费]

    D --> F[获取省份城市列表]
    E --> F
    F --> G[获取资费分类菜单]
    G --> H[逐个分类收集详情]

    H --> I[个人套餐]
    H --> J[企业套餐]
    H --> K[固话宽带]
    H --> L[叠加包服务]
    H --> M[新业务]
    H --> N[营销活动]

    I --> O[数据解析处理]
    J --> O
    K --> O
    L --> O
    M --> O
    N --> O

    O --> P[去重验证]
    P --> Q{结果检查}
    Q -->|全网数据>0 且 本省数据>0| R[批量保存数据库]
    Q -->|任一数据=0| S[任务失败]
    R --> T[更新任务状态完成]

    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style S fill:#ffcdd2
    style Q fill:#fff3e0
```

## 📊 数据流向图

```mermaid
graph LR
    A[联通官网] --> B[系统爬虫]
    B --> C[原始数据]
    C --> D[数据解析器]
    D --> E[结构化数据]
    E --> F[数据验证]
    F --> G[去重处理]
    G --> H[标准格式数据]
    H --> I[数据库存储]
    I --> J[业务系统调用]

    subgraph "数据类型"
        K[个人套餐]
        L[企业套餐]
        M[固话宽带]
        N[叠加包]
        O[新业务]
        P[营销活动]
    end

    D --> K
    D --> L
    D --> M
    D --> N
    D --> O
    D --> P

    style A fill:#ffcdd2
    style I fill:#c8e6c9
    style J fill:#e1f5fe
```

## 📊 处理的数据类型

### 基础资费信息
- **资费名称**：套餐的官方名称
- **资费编号**：联通内部的套餐编码
- **月租费用**：每月固定费用
- **资费单位**：计费单位（元/月、元/次等）

### 套餐内容详情
- **通话时长**：包含的通话分钟数
- **流量额度**：包含的上网流量（GB）
- **短信条数**：包含的短信数量
- **网络类型**：支持的网络（4G/5G）

### 地域信息
- **适用省份**：资费套餐适用的省份
- **适用城市**：具体适用的城市范围
- **全网/本地**：区分全国通用还是本地专属

### 业务分类
- **套餐类型**：个人/家庭/企业
- **业务类别**：移动/固话/宽带/增值服务
- **服务等级**：基础版/标准版/高级版等

## ⚠️ 关键业务规则

### 1. 数据收集规则
- **定时执行**：系统按照预设时间自动执行收集任务
- **全量收集**：每次都收集全部省份的完整数据
- **实时更新**：发现新的资费信息立即收集

### 2. 数据质量规则
- **必填字段检查**：资费名称、价格等关键信息必须完整
- **数据格式验证**：价格必须为数字，日期必须符合格式要求
- **逻辑合理性检查**：价格不能为负数，流量不能超过合理范围

### 3. 去重规则
- **主键去重**：相同资费名称+资费编号视为重复
- **内容去重**：内容完全相同的资费信息只保留一条
- **版本管理**：同一资费的不同版本分别保存

### 4. 异常处理规则
- **网络异常**：遇到网络问题时自动重试3次
- **数据异常**：遇到无法解析的数据时记录日志但不中断流程
- **系统异常**：严重错误时停止任务并发送告警

## 🚨 注意事项和风险控制

### 技术风险
1. **网站结构变化**：联通官网改版可能导致数据收集失败
   - **应对措施**：定期检查和更新收集规则

2. **访问频率限制**：过于频繁的访问可能被网站屏蔽
   - **应对措施**：控制访问频率，模拟真实用户行为

3. **数据格式变化**：联通可能调整数据展示格式
   - **应对措施**：建立灵活的数据解析机制

### 业务风险
1. **数据准确性**：自动收集的数据可能存在理解偏差
   - **应对措施**：建立人工抽查机制

2. **数据时效性**：收集到的数据可能不是最新版本
   - **应对措施**：增加收集频率，建立数据更新提醒

3. **合规风险**：数据收集需要遵守相关法律法规
   - **应对措施**：只收集公开信息，遵守robots协议

### 运维注意事项
1. **监控告警**：建立完善的监控体系，及时发现异常
2. **日志管理**：详细记录每次执行的日志，便于问题排查
3. **数据备份**：定期备份收集到的数据，防止数据丢失
4. **性能优化**：定期优化系统性能，确保收集效率

## 📈 系统价值

### 对业务的价值
- **市场分析**：为市场分析提供及时、准确的竞争对手资费信息
- **决策支持**：为业务决策提供数据支撑
- **趋势分析**：通过历史数据分析市场趋势

### 对运营的价值
- **效率提升**：大幅减少人工收集数据的时间成本
- **质量保证**：标准化的数据格式提高数据质量
- **覆盖全面**：确保数据收集的完整性和及时性

## ❓ 常见问题解答

### Q1: 系统多久收集一次数据？
**A**: 系统可以根据业务需要设置收集频率，通常建议每日收集一次，确保数据的时效性。对于重要的促销活动期间，可以增加到每小时收集一次。

### Q2: 如果联通官网改版了怎么办？
**A**: 系统具有一定的容错能力，但如果官网大幅改版，可能需要技术团队更新收集规则。系统会自动检测异常并发送告警，技术团队会及时处理。

### Q3: 收集到的数据准确性如何保证？
**A**: 系统采用多重验证机制：
- 数据格式验证（价格、日期等格式检查）
- 逻辑合理性检查（价格范围、套餐内容合理性）
- 人工抽查机制（定期人工核验部分数据）

### Q4: 系统是否会影响联通官网的正常运行？
**A**: 不会。系统严格控制访问频率，模拟真实用户行为，不会对联通官网造成压力。同时遵守网站的robots协议和相关规定。

### Q5: 如何获取收集到的数据？
**A**: 业务人员可以通过以下方式获取数据：
- 数据库查询接口
- 数据导出功能
- 定制化报表
- API接口调用

### Q6: 数据收集失败了怎么办？
**A**: 系统具有自动重试机制，如果多次重试仍然失败，会：
- 发送告警通知相关人员
- 记录详细的错误日志
- 尝试备用收集策略
- 必要时启动人工干预

### Q7: 能否收集其他运营商的数据？
**A**: 当前系统专门针对联通设计，如需收集其他运营商数据，需要开发对应的收集模块。系统架构支持扩展，可以相对容易地添加新的运营商支持。

### Q8: 历史数据如何管理？
**A**: 系统会保留所有历史数据，支持：
- 按时间范围查询历史数据
- 数据版本对比分析
- 趋势分析和报表生成
- 数据归档和备份

## 📞 联系方式

如有任何问题或建议，请联系：
- **技术支持**：技术团队
- **业务咨询**：业务团队
- **系统维护**：运维团队

---

*本文档面向业务人员，如需了解技术实现细节，请联系技术团队。*
*文档版本：v1.0 | 更新日期：2025年1月*
