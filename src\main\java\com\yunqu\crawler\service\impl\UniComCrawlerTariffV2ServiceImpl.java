package com.yunqu.crawler.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yunqu.crawler.base.CacheConstants;
import com.yunqu.crawler.base.Constants;
import com.yunqu.crawler.domain.XtyCrawlerTask;
import com.yunqu.crawler.domain.XtyTariffCrawlRecord;
import com.yunqu.crawler.enums.OperatorEnum;
import com.yunqu.crawler.error.CrawlerException;
import com.yunqu.crawler.event.CrawlerTaskEvent;
import com.yunqu.crawler.mapper.XtyCrawlerTaskMapper;
import com.yunqu.crawler.mapper.XtyTariffCrawlRecordMapper;
import com.yunqu.crawler.service.ICrawlerTariffService;
import com.yunqu.crawler.service.IXtyTariffProvinceService;
import com.yunqu.emergency.common.core.utils.SpringUtils;
import com.yunqu.emergency.common.core.utils.StringUtils;
import com.yunqu.emergency.common.mybatis.annotation.Dynamic;
import com.yunqu.emergency.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.cert.X509Certificate;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName UniComCrawlerTariffV2ServiceImpl
 * <AUTHOR> Copy This Tag)
 * @Description TODO 描述文件用途
 * @Since create in 2025/6/19 9:55
 * @Version v1.0
 * @Copyright Copyright (c) 2025
 * @Company 广州云趣信息科技有限公司
 */
@Slf4j
@Service(Constants.OPERATOR_UNICOM + ICrawlerTariffService.BASE_NAME)
public class UniComCrawlerTariffV2ServiceImpl extends UnicomCrawlerTariffServiceImpl {

    // ==================== 内部类定义 ====================

    /**
     * API常量管理 - 集中管理所有硬编码常量
     */
    private static class ApiConstants {
        // API端点
        static final String PROVINCE_ENDPOINT = "/servicequerybusiness/queryTariff/provinceCity";
        static final String TARIFF_TYPE_ENDPOINT = "/servicequerybusiness/queryTariff/TariffMenuDataHomePageNew";
        static final String QG_DICT_ENDPOINT = "/servicequerybusiness/queryTariff/countryTariffQueryChange";
        static final String PERSONAL_TARIFF_ENDPOINT = "/servicequerybusiness/queryTariff/tariffDetailInfoChange";
        static final String OTHER_TARIFF_ENDPOINT = "/servicequerybusiness/queryTariff/TariffMenuDataDetailHomePageChange";

        // 网络类型
        static final String NETWORK_5G = "5G";
        static final String NETWORK_4G = "4G";
        static final String CHOOSE_QG = "qg";

        // 默认参数值
        static final String DEFAULT_VERSION = "WT";
        static final String NATIONWIDE_FLAG = "1";
        static final String LOCAL_FLAG = "0";

        // HTTP请求头常量
        static final String HOST = "m.client.10010.com";
        static final String ACCEPT = "application/json, text/plain, */*";
        static final String ACCEPT_LANGUAGE = "zh-CN,zh;q=0.9";
        static final String ACCEPT_ENCODING = "gzip, deflate, br";
        static final String CONNECTION = "keep-alive";
        static final String CACHE_CONTROL = "no-cache";
        static final String REFERER = "https://m.client.10010.com/";
        static final String SEC_FETCH_DEST = "empty";
        static final String SEC_FETCH_MODE = "cors";
        static final String SEC_FETCH_SITE = "same-origin";

        // 真实浏览器User-Agent池
        static final String[] USER_AGENTS = {
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0"
        };

        // Cookie模板
        static final String COOKIE_TEMPLATE = "SHAREJSESSIONID=%s; servicequerybusiness=1750300808.36.62.798086; acw_tc=%s";
    }

    /**
     * HTTP请求头管理器 - 统一管理HTTP请求头构建和浏览器模拟
     */
    private static class HttpHeaderManager {
        private static final Random random = new Random();

        /**
         * 生成随机User-Agent
         */
        static String getRandomUserAgent() {
            return ApiConstants.USER_AGENTS[random.nextInt(ApiConstants.USER_AGENTS.length)];
        }

        /**
         * 生成动态boundary
         */
        static String generateBoundary() {
            return "--------------------------" + System.currentTimeMillis() + random.nextInt(1000000);
        }

        /**
         * 生成动态SessionId
         */
        static String generateSessionId() {
            return IdUtil.simpleUUID().toUpperCase();
        }

        /**
         * 生成动态acw_tc值
         */
        static String generateAcwTc() {
            long timestamp = System.currentTimeMillis() / 1000;
            return "1a0c639417" + timestamp + "087472e005beefb189ddb61612b52f4744792c41906";
        }

        /**
         * 构建完整的Cookie字符串
         */
        static String buildCookie() {
            return String.format(ApiConstants.COOKIE_TEMPLATE,
                generateSessionId(),
                generateAcwTc());
        }

        /**
         * 为Request.Builder添加标准浏览器请求头
         */
        static void addBrowserHeaders(Request.Builder builder, String boundary) {
            builder.addHeader("User-Agent", getRandomUserAgent())
                   .addHeader("Accept", ApiConstants.ACCEPT)
                   .addHeader("Accept-Language", ApiConstants.ACCEPT_LANGUAGE)
                   .addHeader("Accept-Encoding", ApiConstants.ACCEPT_ENCODING)
                   .addHeader("Host", ApiConstants.HOST)
                   .addHeader("Connection", ApiConstants.CONNECTION)
                   .addHeader("Cache-Control", ApiConstants.CACHE_CONTROL)
                   .addHeader("Referer", ApiConstants.REFERER)
                   .addHeader("Sec-Fetch-Dest", ApiConstants.SEC_FETCH_DEST)
                   .addHeader("Sec-Fetch-Mode", ApiConstants.SEC_FETCH_MODE)
                   .addHeader("Sec-Fetch-Site", ApiConstants.SEC_FETCH_SITE)
                   .addHeader("Content-Type", "multipart/form-data; boundary=" + boundary)
                   .addHeader("Cookie", buildCookie());
        }
    }

    /**
     * 参数构建器 - 统一管理API请求参数构建逻辑
     */
    private static class ParameterBuilder {

        /**
         * 构建基础参数模板
         */
        static Map<String, Object> buildBaseParams() {
            Map<String, Object> params = new HashMap<>();
            params.put("duanlianjieabc", "");
            params.put("channelCode", "");
            params.put("serviceType", "");
            params.put("saleChannel", "");
            params.put("externalSources", "");
            params.put("contactCode", "");
            params.put("version", ApiConstants.DEFAULT_VERSION);
            return params;
        }

        /**
         * 网络类型参数构建结果
         */
        static class NetworkTypeParams {
            final String choose;
            final String type5g;
            final String isOld;
            final String isKey;

            NetworkTypeParams(String choose, String type5g, String isOld, String isKey) {
                this.choose = choose;
                this.type5g = type5g;
                this.isOld = isOld;
                this.isKey = isKey;
            }
        }

        /**
         * 根据网络类型构建参数（保持原有逻辑完全不变）
         */
        static NetworkTypeParams buildNetworkTypeParams(String nameTwo, String sortTwo) {
            if (StringUtils.equals(ApiConstants.NETWORK_5G, nameTwo)) {
                return new NetworkTypeParams(ApiConstants.CHOOSE_QG, ApiConstants.NETWORK_5G, "", "");
            } else if (StringUtils.equals(ApiConstants.NETWORK_4G, nameTwo)) {
                return new NetworkTypeParams(ApiConstants.CHOOSE_QG, "", "", "");
            } else if(StringUtils.equals("4G政企资费", nameTwo)) {
                return new NetworkTypeParams("zq", "", "", "govenment");
            }  else if(StringUtils.equals("5G政企资费", nameTwo)) {
                return new NetworkTypeParams("zq", "5G", "", "govenment");
            } else {
                return new NetworkTypeParams("", "", "0" + sortTwo, "");
            }
        }
    }

    /**
     * 方法参数封装 - 简化复杂参数传递
     */
    private static class AnalysisParams {
        final Long taskId;
        final Integer dateId;
        final String taskProvinceName;
        final String nameOne;
        final String nameTwo;
        final String versionNo;
        final String provinceCode;
        final String provinceName;
        final boolean isNationwide;

        AnalysisParams(XtyCrawlerTask task, String taskProvinceName, String nameOne, String nameTwo,
                       String provinceCode, String provinceName) {
            this(task, taskProvinceName, nameOne, nameTwo, provinceCode, provinceName, false);
        }

        AnalysisParams(XtyCrawlerTask task, String taskProvinceName, String nameOne, String nameTwo,
                       String provinceCode, String provinceName, boolean isNationwide) {
            this.taskId = task.getId();
            this.dateId = task.getDateId();
            this.taskProvinceName = taskProvinceName;
            this.nameOne = nameOne;
            this.nameTwo = nameTwo;
            this.versionNo = task.getVersionNo();
            this.provinceCode = provinceCode;
            this.provinceName = provinceName;
            this.isNationwide = isNationwide;
        }
    }

    // ==================== 原有字段定义 ====================

    @Value("${crawler.unicom-req-base-url}")
    private String unicomReqBaseUrl;


    @Autowired
    public UniComCrawlerTariffV2ServiceImpl(XtyCrawlerTaskMapper crawlerTaskMapper,
                                            IXtyTariffProvinceService tariffProvinceService, XtyTariffCrawlRecordMapper crawlerRecordMapper) {
        super(crawlerTaskMapper, tariffProvinceService, crawlerRecordMapper);
    }

    private static final String GET_PROVINCE_URL = "/servicequerybusiness/queryTariff/provinceCity";

    private static final String GET_TARIFF_TYPE_URL = "/servicequerybusiness/queryTariff/TariffMenuDataHomePageNew";

    private static final String GET_ALLQG_TARIFF_DICT_URL = "/servicequerybusiness/queryTariff/countryTariffQueryChange";

    /**
     * 获取个人资费详情
     */
    private static final String GET_PERSONAL_TARIFF_URL = "/servicequerybusiness/queryTariff/tariffDetailInfoChange";

    /**
     * 获取套餐详情
     */
    private static final String GET_OTHER_TARIFF_URL = "/servicequerybusiness/queryTariff/TariffMenuDataDetailHomePageChange";

    /**
     * 执行请求爬虫
     *
     * @param task 爬虫任务
     */
    @Override
    public void excueteRequestCrawler(XtyCrawlerTask task) {
        try {
            SpringUtils.getAopProxy(this).excuete(task, task.getVersionNo());
        } catch (CrawlerException e) {
            log.error(e.getMessage(), e);
            task.setStatus(0);
            task.setMemo(e.getMessage());
            crawlerTaskMapper.updateById(task);
        }
    }

    @Dynamic(version = "versionNo")
    @Transactional(rollbackFor = {Exception.class, CrawlerException.class})
    protected void excuete(XtyCrawlerTask task, String versionNo) {
        try {
            // 1:请求获取全国数据
            int nationwdeTariffCount = analysisNationwideData(task);

            // 2:请求获取省份数据
            int localTariffCount = analysisLocalProvinceData(task, task.getVersionNo());

            if (localTariffCount == 0 || nationwdeTariffCount == 0) {
                log.warn("{} [FILE-WARN] 本省资费或全网资费爬取异常, 忽略更新任务状态, 运营商：{}{}, localProvinceCount:{}, groupCount:{}", LOG_PREFIX, provincesName, OperatorEnum.UNICOM.getName(), localTariffCount, nationwdeTariffCount);
                throw new CrawlerException("本省资费或全网资费爬取异常");
            }

            task.setLocalProvinceCount(localTariffCount);
            task.setGroupCount(nationwdeTariffCount);
            task.setStatus(2);
            task.setCrawlerEndTime(new Date());
            crawlerTaskMapper.updateById(task);
            SpringUtils.publishEvent(new CrawlerTaskEvent(task.getId()));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    protected int analysisLocalProvinceData(XtyCrawlerTask task, String versionNo) throws IOException {
        log.info("开始爬取本省数据, 任务信息: {}", task);
        // 使用通用处理方法，传入 isNationwide = false
        return processAnalysisData(task, false);
    }

    public int analysisNationwideData(XtyCrawlerTask task) throws IOException {
        // 使用通用处理方法，传入 isNationwide = true
        return processAnalysisData(task, true);
    }

    /**

    /**
     * 处理记录去重和添加到结果列表
     */
    private void processRecordDeduplication(List<XtyTariffCrawlRecord> records,
                                          List<String> processedRecordKeys,
                                          List<XtyTariffCrawlRecord> resultList,
                                          String crawlerType) {
        if (records == null || records.isEmpty()) {
            return;
        }

        for (XtyTariffCrawlRecord record : records) {
            String name = StringUtils.trimToEmpty(record.getName());
            String tariffNo = StringUtils.trimToEmpty(record.getTariffNo());
            String recordKey = name + tariffNo;

            if (processedRecordKeys.contains(recordKey)) {
                continue;  // 跳过重复记录
            }

            processedRecordKeys.add(recordKey);
            record.setCrawlerType(crawlerType);
            resultList.add(record);
        }
    }

    /**
     * 构建资费请求参数
     */
    private Map<String, String> buildTariffRequestParams(List<JSONObject> list, String isItNationwide) {
        Map<String, String> getTariffParam = new HashMap<>();
        ParameterBuilder.buildBaseParams().forEach((k, v) -> getTariffParam.put(k, v.toString()));
        getTariffParam.put("isItNationwide", isItNationwide);
        getTariffParam.put("tariffDetailReq", JSONUtil.toJsonStr(list));
        getTariffParam.put("page", "1");
        getTariffParam.put("size", String.valueOf(list.size()));
        return getTariffParam;
    }

    /**
     * 处理网络类型的资费数据 - 核心业务逻辑提取
     */
    private List<XtyTariffCrawlRecord> processNetworkTypeData(JSONObject object, String nameOne, String nameTwo,
                                                            String sortTwo, String provCode, String cityCode,
                                                            AnalysisParams analysisParams) {
        String linkFlag = object.getStr("linkFlag");

        if (StringUtils.isNotBlank(linkFlag)) {
            // 处理有 linkFlag 的情况
            return processLinkFlagData(nameTwo, sortTwo, provCode, cityCode, analysisParams, nameOne);
        } else {
            // 处理三级对象列表
            return processThreeObjectListData(object, nameOne, nameTwo, provCode, cityCode, analysisParams);
        }
    }

    /**
     * 处理有 linkFlag 的数据
     */
    private List<XtyTariffCrawlRecord> processLinkFlagData(String nameTwo, String sortTwo, String provCode,
                                                         String cityCode, AnalysisParams analysisParams, String nameOne) {
        try {
            // 使用参数构建器构建基础参数
            Map<String, Object> param = ParameterBuilder.buildBaseParams();
            param.put("isItNationwide", analysisParams.isNationwide ? ApiConstants.NATIONWIDE_FLAG : ApiConstants.LOCAL_FLAG);
            param.put("isRemove", "");
            param.put("isKey", "");
            param.put("provinceId", provCode);
            param.put("cityId", cityCode);

            // 使用参数构建器处理网络类型参数
            ParameterBuilder.NetworkTypeParams networkParams = ParameterBuilder.buildNetworkTypeParams(nameTwo, sortTwo);

            // 根据网络类型设置对应参数（保持原有逻辑）
            if (StringUtils.equals(ApiConstants.NETWORK_5G, nameTwo)) {
                param.put("choose", ApiConstants.CHOOSE_QG);
                param.put("type5g", ApiConstants.NETWORK_5G);
            } else if (StringUtils.equals(ApiConstants.NETWORK_4G, nameTwo)) {
                param.put("choose", ApiConstants.CHOOSE_QG);
            } else if(StringUtils.equals("4G政企资费", nameTwo)) {
                param.put("choose", "zq");
                param.put("isKey", "govenment");
            } else if(StringUtils.equals("5G政企资费", nameTwo)) {
                param.put("choose", "zq");
                param.put("type5g", "5G");
                param.put("isKey", "govenment");
            } else {
                param.put("isOld", networkParams.isOld);
            }

            List<JSONObject> allQGDictList = getAllQGList(param);
            if (allQGDictList == null || allQGDictList.isEmpty()) {
                // 如果没有QG数据，处理三级对象列表
                return processThreeObjectListData(null, nameOne, nameTwo, provCode, cityCode, analysisParams);
            } else {
                // 处理QG字典数据
                return processQGDictListData(allQGDictList, networkParams, provCode, cityCode, analysisParams);
            }
        } catch (IOException e) {
            log.error("处理linkFlag数据失败, nameTwo: {}, provCode: {}, cityCode: {}, 异常: {}",
                nameTwo, provCode, cityCode, e.getMessage(), e);
            return new ArrayList<>(); // 返回空列表，不中断整个流程
        }
    }

    /**
     * 处理三级对象列表数据
     */
    private List<XtyTariffCrawlRecord> processThreeObjectListData(JSONObject object, String nameOne, String nameTwo,
                                                                String provCode, String cityCode, AnalysisParams analysisParams) {
        List<JSONObject> threeObjectList = null;
        if (object != null) {
            threeObjectList = object.getBeanList("threeObjectList", JSONObject.class);
        }

        if (threeObjectList == null || threeObjectList.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            String ids = CollUtil.join(threeObjectList.stream().map(o -> o.getStr("idThird")).toList(), ",");
            Map<String, String> getTariffParam = new HashMap<>();
            getTariffParam.put("version", ApiConstants.DEFAULT_VERSION);
            getTariffParam.put("isItNationwide", analysisParams.isNationwide ? ApiConstants.NATIONWIDE_FLAG : ApiConstants.LOCAL_FLAG);
            getTariffParam.put("levelThreeMenuId", ids);
            getTariffParam.put("page", "1");
            getTariffParam.put("size", String.valueOf(threeObjectList.size()));
            getTariffParam.put("provinceId", provCode);
            getTariffParam.put("cityId", cityCode);

            JSONArray otherTariffList = getOtherTariffList(getTariffParam);
            return analysisThreeDetailData(otherTariffList, analysisParams.taskId,
                    analysisParams.dateId, analysisParams.taskProvinceName, analysisParams.isNationwide ? "全网资费" : analysisParams.provinceName + "资费", "", "",
                    analysisParams.nameOne, analysisParams.nameTwo, "", analysisParams.versionNo,
                    analysisParams.provinceCode, analysisParams.provinceName);
        } catch (IOException e) {
            log.error("处理三级对象列表数据失败, provCode: {}, cityCode: {}, 异常: {}", provCode, cityCode, e.getMessage(), e);
            return new ArrayList<>(); // 返回空列表，不中断整个流程
        }
    }

    /**
     * 处理QG字典列表数据
     */
    private List<XtyTariffCrawlRecord> processQGDictListData(List<JSONObject> allQGDictList,
                                                           ParameterBuilder.NetworkTypeParams networkParams,
                                                           String provCode, String cityCode, AnalysisParams analysisParams) {
        List<JSONObject> list = allQGDictList.stream().map(o -> {
            JSONObject result = new JSONObject();
            result.put("id", o.getStr("id"));
            result.put("packageNameCode", o.getStr("packagetypeid"));
            result.put("tariffDetailId", o.getStr("detailid"));
            result.put("choose", networkParams.choose);
            result.put("isRemove", "");
            result.put("isComboSet", StringUtils.trimToEmpty(o.getStr("isComboSet")));
            result.put("isKey", networkParams.isKey);
            result.put("isOld", networkParams.isOld);
            result.put("type5g", networkParams.type5g);
            result.put("provinceId", provCode);
            result.put("cityId", cityCode);
            return result;
        }).toList();

        try {
            // 使用提取的方法构建资费请求参数
            Map<String, String> getTariffParam = buildTariffRequestParams(list,
                    analysisParams.isNationwide ? ApiConstants.NATIONWIDE_FLAG : ApiConstants.LOCAL_FLAG);
            JSONArray tariffList = getAllPersonalTariffList(getTariffParam);

            return analysisTariffDetailInfoList(tariffList, analysisParams.taskId,
                    analysisParams.dateId, analysisParams.taskProvinceName, analysisParams.isNationwide ? "全网资费" : analysisParams.provinceName + "资费", "", "",
                    analysisParams.nameOne, analysisParams.nameTwo, "", analysisParams.versionNo,
                    analysisParams.provinceCode, analysisParams.provinceName);
        } catch (IOException e) {
            log.error("处理QG字典列表数据失败, provCode: {}, cityCode: {}, 异常: {}", provCode, cityCode, e.getMessage(), e);
            return List.of(); // 返回空列表，不中断整个流程
        }
    }

    /**
     * 处理资费类型列表 - 提取二级循环逻辑
     */
    private void processTariffTypeList(List<JSONObject> tariffTypeList, String provCode, String cityCode,
                                     AnalysisParams analysisParams, List<XtyTariffCrawlRecord> resultList,
                                     List<String> processedRecordKeys) {
        for (JSONObject entries : tariffTypeList) {
            String nameOne = entries.getStr("nameOne");
            List<JSONObject> twoObjectDTOList = entries.getBeanList("twoObjectDTOList", JSONObject.class);
            if (twoObjectDTOList == null || twoObjectDTOList.isEmpty()) {
                continue;
            }

            for (JSONObject object : twoObjectDTOList) {
                String nameTwo = object.getStr("nameTwo");
                String sortTwo = object.getStr("sortTwo");

                // 使用提取的方法处理网络类型数据
                List<XtyTariffCrawlRecord> records = processNetworkTypeData(object, nameOne, nameTwo,
                        sortTwo, provCode, cityCode, analysisParams);

                // 使用提取的方法处理记录去重和添加
                String crawlerType = analysisParams.isNationwide ? "全网资费" : analysisParams.provinceName + "资费";
                processRecordDeduplication(records, processedRecordKeys, resultList, crawlerType);
            }
        }
    }

    /**
     * 处理省份数据列表 - 提取一级循环逻辑
     */
    private void processProvinceDataList(List<JSONObject> provinceList, AnalysisParams analysisParams,
                                       List<XtyTariffCrawlRecord> resultList, List<String> processedRecordKeys) {
        for (JSONObject provinceEntries : provinceList) {
            String cityCode = provinceEntries.getStr("cityCode");
            String provCode = provinceEntries.getStr("provCode");

            try {
                // 获取资费类型列表
                List<JSONObject> tariffTypeList = getTariffTypeList(
                        analysisParams.isNationwide ? "1" : "0", provCode, cityCode);

                // 处理资费类型列表
                processTariffTypeList(tariffTypeList, provCode, cityCode, analysisParams, resultList, processedRecordKeys);
            } catch (IOException e) {
                log.error("处理省份数据失败, provCode: {}, cityCode: {}, 异常: {}", provCode, cityCode, e.getMessage(), e);
                // 继续处理下一个省份，不中断整个流程
            }
        }
    }

    /**
     * 通用的数据分析处理方法 - 抽象公共逻辑
     */
    private int processAnalysisData(XtyCrawlerTask task, boolean isNationwide) throws IOException {
        // 获取省份信息
        JSONObject provinceInfo = RedisUtils.getCacheMapValue(CacheConstants.UNICOM_PROVINCE_INFO_DICT_KEY,
                task.getProvinceCode());
        String provinceCode = provinceInfo.getStr("provinceCode");
        String provinceName = provinceInfo.getStr("provinceName");
        String taskProvinceName = provinceInfo.getStr("name");

        // 获取省份列表
        String crawlerProvinceCode = task.getProvinceCode();
        List<JSONObject> provinceList;
        try {
            provinceList = getProvinceList(crawlerProvinceCode);
        } catch (IOException e) {
            log.error("获取省份列表失败, crawlerProvinceCode: {}, 异常: {}", crawlerProvinceCode, e.getMessage(), e);
            throw e; // 重新抛出异常，让上层处理
        }
        if (CollectionUtil.isEmpty(provinceList)) {
            return 0;
        }

        // 初始化数据收集
        List<String> processedRecordKeys = new ArrayList<>();
        List<XtyTariffCrawlRecord> resultList = new ArrayList<>();

        // 创建分析参数
        AnalysisParams analysisParams = new AnalysisParams(task, taskProvinceName, "", "",
                provinceCode, provinceName, isNationwide);

        // 处理省份数据
        processProvinceDataList(provinceList, analysisParams, resultList, processedRecordKeys);

        // 批量插入数据（保持原有的事务边界）
        if (!resultList.isEmpty()) {
            crawlerRecordMapper.insertBatch(resultList);
        }

        return resultList.size();
    }



    /**
     * 统一构建HTTP请求
     * @param endpoint API端点
     * @param params 请求参数
     * @return 构建好的Request对象
     */
    private Request buildHttpRequest(String endpoint, Map<String, ?> params) {
        // 生成动态boundary
        String boundary = HttpHeaderManager.generateBoundary();

        // 构建MultipartBody
        MultipartBody.Builder builder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);

        for (Map.Entry<String, ?> entry : params.entrySet()) {
            builder.addFormDataPart(entry.getKey(), entry.getValue().toString());
        }
        MultipartBody body = builder.build();

        // 构建完整URL
        String baseUrl = StringUtils.isBlank(unicomReqBaseUrl) ? "https://m.client.10010.com" : unicomReqBaseUrl;
        String url = baseUrl + endpoint;

        // 构建Request并添加浏览器请求头
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .method("POST", body);

        HttpHeaderManager.addBrowserHeaders(requestBuilder, boundary.substring(28));

        return requestBuilder.build();
    }

    private OkHttpClient createTrustAllClient() {
        try {
            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public void checkClientTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public void checkServerTrusted(X509Certificate[] chain, String authType) {
                        }

                        @Override
                        public X509Certificate[] getAcceptedIssuers() {
                            return new X509Certificate[0];
                        }
                    }
            };

            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            return new OkHttpClient.Builder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                    .hostnameVerifier((hostname, session) -> true)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Retryable(
        retryFor = {IOException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0, maxDelay = 8000)
    )
    public List<JSONObject> getProvinceList(String provinceCode) throws IOException {
        // 构建请求参数
        Map<String, Object> params = ParameterBuilder.buildBaseParams();
        params.put("provinceId", provinceCode);

        log.info("开始获取省份列表>>>>>{}", JSONUtil.toJsonStr(params));

        // 使用统一的请求构建方法
        OkHttpClient client = createTrustAllClient();
        Request request = buildHttpRequest(ApiConstants.PROVINCE_ENDPOINT, params);
        Response response = client.newCall(request).execute();
        String result = response.body().string();
        log.info("结束获取省份列表>>>>>{}", result);
        JSONObject entries = JSONUtil.parseObj(result);
        List<JSONObject> cityList = entries.getBeanList("cityList", JSONObject.class);
        if (cityList == null) return List.of();
        return cityList;
    }

    @Recover
    public List<JSONObject> recoverGetProvinceList(Exception e, String provinceCode) {
        log.error("获取省份列表最终失败, provinceCode: {}, 异常: {}", provinceCode, e.getMessage(), e);
        return List.of();
    }

    @Retryable(
        retryFor = {IOException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0, maxDelay = 8000)
    )
    public List<JSONObject> getTariffTypeList(String isItNationwide, String provinceCode, String cityId) throws IOException {
        // 构建请求参数
        Map<String, Object> params = ParameterBuilder.buildBaseParams();
        params.put("isItNationwide", isItNationwide);
        params.put("provinceId", provinceCode);
        params.put("cityId", cityId);

        // 使用统一的请求构建方法
        OkHttpClient client = createTrustAllClient();
        Request request = buildHttpRequest(ApiConstants.TARIFF_TYPE_ENDPOINT, params);
        String url = (StringUtils.isBlank(unicomReqBaseUrl) ? "https://m.client.10010.com" : unicomReqBaseUrl) + ApiConstants.TARIFF_TYPE_ENDPOINT;
        log.info("开始获取资费二级分类数据字典[{}]>>>>>{}", url, JSONUtil.toJsonStr(params));
        Response response = client.newCall(request).execute();
        String result = response.body().string();
        log.info("结束获取资费二级分类数据字典>>>>>{}", result);
        JSONObject entries = JSONUtil.parseObj(result);
        List<JSONObject> oneTwoMenusList = entries.getBeanList("oneTwoMenusList", JSONObject.class);
        if (oneTwoMenusList == null) return List.of();
        return oneTwoMenusList;
    }

    @Recover
    public List<JSONObject> recoverGetTariffTypeList(Exception e, String isItNationwide, String provinceCode, String cityId) {
        log.error("获取资费类型列表最终失败, isItNationwide: {}, provinceCode: {}, cityId: {}, 异常: {}",
            isItNationwide, provinceCode, cityId, e.getMessage(), e);
        return List.of();
    }

    /**
     * 获取所有个人资费字典
     *
     * @param params 请求参数
     * @return 个人资费字典数组
     */
    @Retryable(
        retryFor = {IOException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0, maxDelay = 8000)
    )
    private JSONArray getAllPersonalTariffList(Map<String, String> params) throws IOException {
        log.info("开始获取所有个人资费字典>>>>>{}", JSONUtil.toJsonStr(params));

        // 使用统一的请求构建方法
        OkHttpClient client = createTrustAllClient();
        Request request = buildHttpRequest(ApiConstants.PERSONAL_TARIFF_ENDPOINT, params);
        Response response = client.newCall(request).execute();
        String result = response.body().string();
        log.info("结束获取所有个人资费字典>>>>>{}", result);
        JSONObject entries = JSONUtil.parseObj(result);
        JSONArray jsonArray = entries.getJSONArray("tariffDetailInfoList");
        if (jsonArray == null) return new JSONArray();
        return jsonArray;
    }

    @Recover
    private JSONArray recoverGetAllPersonalTariffList(Exception e, Map<String, String> params) {
        log.error("获取个人资费字典最终失败, params: {}, 异常: {}", JSONUtil.toJsonStr(params), e.getMessage(), e);
        return new JSONArray();
    }

    /**
     * 获取所有套餐字典
     *
     * @param params 请求参数
     * @return 套餐字典数组
     */
    @Retryable(
        retryFor = {IOException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0, maxDelay = 8000)
    )
    private JSONArray getOtherTariffList(Map<String, String> params) throws IOException {
        log.info("开始获取所有套餐字典>>>>>{}", JSONUtil.toJsonStr(params));

        // 创建带有扩展超时的客户端
        OkHttpClient client = createTrustAllClient()
                .newBuilder()
                .callTimeout(Duration.of(10, ChronoUnit.MINUTES))
                .connectTimeout(Duration.of(10, ChronoUnit.MINUTES))
                .readTimeout(10, TimeUnit.MINUTES)
                .build();

        // 使用统一的请求构建方法
        Request request = buildHttpRequest(ApiConstants.OTHER_TARIFF_ENDPOINT, params);
        Response response = client.newCall(request).execute();
        String result = response.body().string();
        log.info("结束获取所有套餐字典>>>>>{}", result);
        JSONObject entries = JSONUtil.parseObj(result);
        JSONArray jsonArray = entries.getJSONArray("threeDetailDate");
        if (jsonArray == null) return new JSONArray();
        return jsonArray;
    }

    @Recover
    private JSONArray recoverGetOtherTariffList(Exception e, Map<String, String> params) {
        log.error("获取套餐字典最终失败, params: {}, 异常: {}", JSONUtil.toJsonStr(params), e.getMessage(), e);
        return new JSONArray();
    }

    /**
     * 获取所有套餐字典
     *
     * @param params 请求参数
     * @return 套餐字典列表
     */
    @Retryable(
        retryFor = {IOException.class, RuntimeException.class},
        maxAttempts = 3,
        backoff = @Backoff(delay = 1000, multiplier = 2.0, maxDelay = 8000)
    )
    public List<JSONObject> getAllQGList(Map<String, Object> params) throws IOException {
        log.info("开始获取二级分类所有资费字典>>>>>{}", JSONUtil.toJsonStr(params));

        // 使用统一的请求构建方法
        OkHttpClient client = createTrustAllClient();
        Request request = buildHttpRequest(ApiConstants.QG_DICT_ENDPOINT, params);
        Response response = client.newCall(request).execute();
        String result = response.body().string();
        log.info("结束获取二级分类所有资费字典>>>>>{}", result);
        JSONObject entries = JSONUtil.parseObj(result);
        List<JSONObject> tariffTypeList = entries.getBeanList("tariffTypeList", JSONObject.class);
        if (tariffTypeList == null) return List.of();
        return tariffTypeList;
    }

    @Recover
    public List<JSONObject> recoverGetAllQGList(Exception e, Map<String, Object> params) {
        log.error("获取二级分类资费字典最终失败, params: {}, 异常: {}", JSONUtil.toJsonStr(params), e.getMessage(), e);
        return List.of();
    }

    public static void main(String[] args) throws IOException {
        UniComCrawlerTariffV2ServiceImpl service = new UniComCrawlerTariffV2ServiceImpl(null, null, null);
        System.out.println(service.getTariffTypeList("1", "018", "188"));
    }
}
